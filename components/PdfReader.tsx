"use client"

import { useState, useEffect, useRef, useCallback } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { ChevronLeft, ChevronRight, BookOpen, Settings, X, ZoomIn, ZoomOut, RotateCw } from "lucide-react"
import { motion, AnimatePresence } from "framer-motion"
import { useNavigation } from "@/contexts/NavigationContext"

interface PdfReaderProps {
  pdfUrl: string
  bookTitle: string
  authorName: string
  projectId: string
  userId?: string
  onClose: () => void
  isPreview?: boolean
  onApprovePreview?: () => void
}

export function PdfReader({ 
  pdfUrl, 
  bookTitle, 
  authorName, 
  projectId, 
  userId, 
  onClose, 
  isPreview = false, 
  onApprovePreview 
}: PdfReaderProps) {
  const { hideNavigation, showNavigation } = useNavigation()

  // Core reader state
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(0)
  const [zoom, setZoom] = useState(1.0)
  const [rotation, setRotation] = useState(0)
  const [theme, setTheme] = useState('light')
  const [showSettings, setShowSettings] = useState(false)
  const [readingProgress, setReadingProgress] = useState(0)
  const [isPageTurning, setIsPageTurning] = useState(false)

  // Refs
  const iframeRef = useRef<HTMLIFrameElement>(null)
  const pageRef = useRef<HTMLDivElement>(null)

  // Hide navigation when reader opens, show when it closes
  useEffect(() => {
    console.log('PdfReader component mounted with pdfUrl:', pdfUrl)
    hideNavigation()
    return () => {
      showNavigation()
    }
  }, [hideNavigation, showNavigation, pdfUrl])

  // Update reading progress when page changes
  useEffect(() => {
    if (totalPages > 0) {
      const progress = (currentPage / totalPages) * 100
      setReadingProgress(progress)
    }
  }, [currentPage, totalPages])

  // Wrapper function to ensure navigation is shown when closing
  const handleClose = useCallback(() => {
    showNavigation()
    onClose()
  }, [onClose, showNavigation])

  // Page navigation functions
  const nextPage = useCallback(() => {
    if (currentPage < totalPages) {
      setIsPageTurning(true)
      setTimeout(() => {
        setCurrentPage(prev => prev + 1)
        if (iframeRef.current) {
          iframeRef.current.src = `${pdfUrl}#page=${currentPage + 1}`
        }
        setIsPageTurning(false)
      }, 150)
    }
  }, [currentPage, totalPages, pdfUrl])

  const prevPage = useCallback(() => {
    if (currentPage > 1) {
      setIsPageTurning(true)
      setTimeout(() => {
        setCurrentPage(prev => prev - 1)
        if (iframeRef.current) {
          iframeRef.current.src = `${pdfUrl}#page=${currentPage - 1}`
        }
        setIsPageTurning(false)
      }, 150)
    }
  }, [currentPage, pdfUrl])

  const goToPage = (page: number) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page)
      if (iframeRef.current) {
        iframeRef.current.src = `${pdfUrl}#page=${page}`
      }
    }
  }

  // Zoom functions
  const zoomIn = () => setZoom(prev => Math.min(prev + 0.25, 3.0))
  const zoomOut = () => setZoom(prev => Math.max(prev - 0.25, 0.5))
  const resetZoom = () => setZoom(1.0)

  // Rotation function
  const rotate = () => setRotation(prev => (prev + 90) % 360)

  // Keyboard navigation
  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      if (e.key === 'ArrowRight' || e.key === ' ') {
        e.preventDefault()
        nextPage()
      } else if (e.key === 'ArrowLeft') {
        e.preventDefault()
        prevPage()
      } else if (e.key === 'Escape') {
        handleClose()
      }
    }

    window.addEventListener('keydown', handleKeyPress)
    return () => window.removeEventListener('keydown', handleKeyPress)
  }, [nextPage, prevPage, handleClose])

  // Initialize total pages (you might need to detect this from PDF)
  useEffect(() => {
    // For now, set a default. In a real implementation, you'd detect this from the PDF
    setTotalPages(100) // This should be detected from the actual PDF
  }, [pdfUrl])

  return (
    <div className={`fixed inset-0 z-50 ${theme === 'dark' ? 'dark' : ''}`}>
      <div className="h-full bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 flex flex-col">
        
        {/* Header */}
        <div className="border-b border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center justify-between max-w-6xl mx-auto">
            <div className="flex items-center space-x-4">
              <Button variant="ghost" size="sm" onClick={handleClose} title="Close Reader">
                <X className="h-4 w-4" />
              </Button>
              <div>
                <h1 className="font-semibold text-lg">{bookTitle}</h1>
                <p className="text-sm opacity-70">by {authorName}</p>
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              <Button variant="ghost" size="sm" onClick={() => setShowSettings(!showSettings)} title="Reader Settings">
                <Settings className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* Enhanced Progress Bar */}
        <div className="px-4 py-2 border-b border-gray-200 dark:border-gray-700">
          <div className="h-1 bg-gray-200 dark:bg-gray-700 rounded-full">
            <div
              className="h-full bg-blue-600 transition-all duration-300 rounded-full"
              style={{ width: `${readingProgress}%` }}
            />
          </div>
          <div className="text-xs opacity-70 mt-1 text-center">
            Page {currentPage} of {totalPages} • {Math.round(readingProgress)}% complete
          </div>
        </div>

        <div className="flex h-full">
          {/* Settings Sidebar */}
          {showSettings && (
            <div className="w-80 border-r border-gray-200 dark:border-gray-700 overflow-y-auto">
              <div className="p-4">
                <h3 className="font-semibold mb-4">PDF Reader Settings</h3>
                
                <div className="space-y-6">
                  {/* Zoom Controls */}
                  <div>
                    <label className="block text-sm font-medium mb-2">Zoom Level</label>
                    <div className="flex items-center space-x-2">
                      <Button variant="outline" size="sm" onClick={zoomOut} disabled={zoom <= 0.5}>
                        <ZoomOut className="h-4 w-4" />
                      </Button>
                      <span className="text-sm min-w-16 text-center">{Math.round(zoom * 100)}%</span>
                      <Button variant="outline" size="sm" onClick={zoomIn} disabled={zoom >= 3.0}>
                        <ZoomIn className="h-4 w-4" />
                      </Button>
                      <Button variant="outline" size="sm" onClick={resetZoom}>
                        Reset
                      </Button>
                    </div>
                  </div>

                  {/* Rotation */}
                  <div>
                    <label className="block text-sm font-medium mb-2">Rotation</label>
                    <Button variant="outline" size="sm" onClick={rotate}>
                      <RotateCw className="h-4 w-4 mr-2" />
                      Rotate ({rotation}°)
                    </Button>
                  </div>

                  {/* Theme */}
                  <div>
                    <label className="block text-sm font-medium mb-2">Theme</label>
                    <select 
                      value={theme}
                      onChange={(e) => setTheme(e.target.value)}
                      className="w-full p-2 border rounded-md bg-background"
                    >
                      <option value="light">Light</option>
                      <option value="dark">Dark</option>
                    </select>
                  </div>

                  {/* Page Navigation */}
                  <div>
                    <label className="block text-sm font-medium mb-2">Go to Page</label>
                    <div className="flex items-center space-x-2">
                      <input
                        type="number"
                        min="1"
                        max={totalPages}
                        value={currentPage}
                        onChange={(e) => {
                          const page = parseInt(e.target.value)
                          if (!isNaN(page)) {
                            goToPage(page)
                          }
                        }}
                        className="w-20 p-2 border rounded-md bg-background text-center"
                      />
                      <span className="text-sm opacity-70">of {totalPages}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Main PDF Display */}
          <div className="flex-1 flex flex-col">
            <motion.div
              ref={pageRef}
              className="flex-1 overflow-hidden relative"
              animate={{ opacity: isPageTurning ? 0.7 : 1 }}
              transition={{ duration: 0.15 }}
            >
              <div 
                className="w-full h-full flex items-center justify-center p-4"
                style={{
                  transform: `scale(${zoom}) rotate(${rotation}deg)`,
                  transformOrigin: 'center center',
                  transition: 'transform 0.3s ease'
                }}
              >
                <iframe
                  ref={iframeRef}
                  src={`${pdfUrl}#page=${currentPage}`}
                  className="w-full h-full border-0 rounded-lg shadow-lg"
                  title={`${bookTitle} - Page ${currentPage}`}
                />
              </div>
            </motion.div>

            {/* Navigation Footer */}
            <div className="border-t border-gray-200 dark:border-gray-700 p-4">
              <div className="flex items-center justify-between max-w-4xl mx-auto">
                <Button 
                  variant="outline"
                  onClick={prevPage}
                  disabled={currentPage === 1}
                >
                  <ChevronLeft className="h-4 w-4 mr-1" />
                  Previous
                </Button>
                
                <div className="text-sm opacity-70">
                  Page {currentPage} of {totalPages}
                </div>
                
                <Button 
                  variant="outline"
                  onClick={nextPage}
                  disabled={currentPage === totalPages}
                >
                  Next
                  <ChevronRight className="h-4 w-4 ml-1" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Floating Action Buttons for Preview Mode */}
      {isPreview && (
        <div className="fixed bottom-6 right-6 z-50 flex flex-col gap-3">
          {/* Close Preview Button */}
          <Button
            onClick={handleClose}
            variant="outline"
            className="bg-white hover:bg-gray-50 text-gray-700 shadow-lg px-6 py-3 text-base font-semibold rounded-full border-2"
            size="lg"
          >
            ← Close Preview
          </Button>

          {/* Approve & Publish Button */}
          {onApprovePreview && (
            <Button
              onClick={onApprovePreview}
              className="bg-green-600 hover:bg-green-700 text-white shadow-lg px-6 py-3 text-base font-semibold rounded-full"
              size="lg"
            >
              ✓ Approve & Publish Book
            </Button>
          )}
        </div>
      )}
    </div>
  )
}

export default PdfReader
